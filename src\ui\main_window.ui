<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>620</width>
    <height>584</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="1">
    <widget class="CardWidget" name="CardWidget">
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="2">
         <widget class="CaptionLabel" name="CaptionLabel_2">
          <property name="text">
           <string>1ms/div</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="DoubleSpinBox" name="hori_div_spinbox"/>
        </item>
        <item row="0" column="0">
         <widget class="CaptionLabel" name="CaptionLabel">
          <property name="text">
           <string>时基挡位</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="SwitchButton" name="sw_mode">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>实时波形显示模式</string>
        </property>
        <property name="onText">
         <string>录波预览模式</string>
        </property>
        <property name="offText">
         <string>实时波形显示模式</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="RadioButton" name="radiobtn_recording_wave_enable">
        <property name="text">
         <string>录波使能</string>
        </property>
        <property name="autoExclusive">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="RadioButton" name="radioButton">
        <property name="text">
         <string>自动滚动</string>
        </property>
        <property name="autoExclusive">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="PushButton" name="btn_recording_preview">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>录波预览</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="HyperlinkButton" name="reload_conf_btn">
        <property name="text">
         <string>重新载入配置文件</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="global_ctrl_widget" native="true">
        <layout class="QGridLayout" name="gridLayout_3">
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="global_ctrl_widget_layout"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QTabWidget" name="ch_ctrl_widget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Times New Roman</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tab_ch">
         <attribute name="title">
          <string>CH1</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_5">
          <item row="0" column="0">
           <widget class="ScrollArea" name="ch_scroll_area_contents">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <property name="widgetResizable">
             <bool>true</bool>
            </property>
            <widget class="QWidget" name="scrollAreaWidgetContents">
             <property name="geometry">
              <rect>
               <x>0</x>
               <y>0</y>
               <width>212</width>
               <height>270</height>
              </rect>
             </property>
             <layout class="QGridLayout" name="gridLayout_4">
              <item row="0" column="0">
               <layout class="QVBoxLayout" name="ch_scroll_area_contents_layout"/>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QWidget" name="scope_widget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>Times New Roman</family>
       <pointsize>12</pointsize>
      </font>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>HyperlinkButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SwitchButton</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>RadioButton</class>
   <extends>QRadioButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ScrollArea</class>
   <extends>QScrollArea</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>CaptionLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>DoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
